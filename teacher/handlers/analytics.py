from aiogram import Router, F
from aiogram.fsm.state import StatesGroup, State
from common.analytics.register_handlers import register_analytics_handlers
from common.microtopics.register_handlers import register_microtopics_handlers


# Используем базовые состояния
class TeacherAnalyticsStates(StatesGroup):
    main = State()
    select_group_for_student = State()
    select_student = State()
    student_stats = State()
    student_stats_display = State()  # Новое состояние для отображения статистики студента
    select_group_for_group = State()
    group_stats = State()
    group_stats_display = State()  # Новое состояние для отображения статистики группы
    select_subject = State()
    subject_stats = State()
    subject_stats_display = State()  # Новое состояние для отображения статистики предмета

    # Состояния для микротем
    detailed_stats = State()  # Детальная статистика по микротемам
    summary_stats = State()   # Сводка сильных/слабых тем

router = Router()

register_analytics_handlers(router, TeacherAnalyticsStates, 'teacher')

# Регистрируем универсальные обработчики микротем для учителя
register_microtopics_handlers(
    router=router,
    states_group=TeacherAnalyticsStates,
    role="teacher",
    detailed_callback_prefix="teacher_microtopics_page",
    summary_callback_prefix="teacher_summary_page",
    detailed_state=TeacherAnalyticsStates.detailed_stats,
    summary_state=TeacherAnalyticsStates.summary_stats,
    subject_details_state=TeacherAnalyticsStates.student_stats,
    back_keyboard_func=lambda: None,  # Будет определено в универсальной функции
    title="📌 Статистика ученика\n📈 % понимания по микротемам",
    items_per_page_detailed=15,
    items_per_page_summary=15,
    premium_check=False  # В аналитике премиум не проверяем
)