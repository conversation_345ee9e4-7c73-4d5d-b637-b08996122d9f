import logging
from aiogram import F, Router
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext
from .handlers import (
    handle_microtopics_pagination_universal,
    show_detailed_microtopics_from_callback,
    show_summary_microtopics_from_callback,
    handle_back_from_microtopics_image
)


# Настройка логирования
logging.basicConfig(level=logging.INFO)


def register_microtopics_handlers(
    router: Router,
    states_group,
    role: str,
    detailed_callback_prefix: str,
    summary_callback_prefix: str,
    detailed_state,
    summary_state,
    subject_details_state,
    back_keyboard_func,
    title: str,                      # Заголовок для микротем
    items_per_page_detailed: int = 15,
    items_per_page_summary: int = 15,
    premium_check: bool = True
):
    """
    Регистрация универсальных обработчиков микротем для роли

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний роли
        role: Роль пользователя ("student", "curator", "teacher", etc.)
        detailed_callback_prefix: Префикс для детальной пагинации (например: "student_microtopics_page")
        summary_callback_prefix: Префикс для пагинации сводки (например: "student_summary_page")
        detailed_state: Состояние детальной статистики
        summary_state: Состояние сводки
        subject_details_state: Состояние выбора предмета
        back_keyboard_func: Функция для кнопки "Назад"
        items_per_page_detailed: Элементов на страницу для детальной статистики
        items_per_page_summary: Элементов на страницу для сводки
        premium_check: Нужна ли проверка премиума
    """

    # Основные обработчики отображения микротем
    @router.callback_query(subject_details_state, F.data.startswith("microtopics_detailed_"))
    async def show_detailed_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_detailed_microtopics | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_detailed_microtopics_from_callback(
            callback=callback,
            state=state,
            role=role,
            target_state=detailed_state,
            callback_prefix=detailed_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            title=title,
            items_per_page=items_per_page_detailed,
            caption="📊 Детальная статистика по микротемам",
            premium_check=premium_check,
            premium_feature="detailed_analytics"
        )

    @router.callback_query(subject_details_state, F.data.startswith("microtopics_summary_"))
    async def show_summary_microtopics(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: show_summary_microtopics | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await show_summary_microtopics_from_callback(
            callback=callback,
            state=state,
            role=role,
            target_state=summary_state,
            callback_prefix=summary_callback_prefix,
            back_keyboard_func=back_keyboard_func,
            title=title,
            items_per_page=items_per_page_summary,
            caption="📊 Сводка по сильным и слабым темам",
            premium_check=premium_check,
            premium_feature="advanced_statistics"
        )

    # Обработчики пагинации для детальной статистики микротем
    @router.callback_query(detailed_state, F.data.startswith(f"{detailed_callback_prefix}_"))
    async def handle_detailed_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_detailed_microtopics_pagination | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=detailed_callback_prefix,
            display_mode="detailed",
            role=role
        )

    # Обработчики пагинации для сводки микротем
    @router.callback_query(summary_state, F.data.startswith(f"{summary_callback_prefix}_"))
    async def handle_summary_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: handle_summary_microtopics_pagination | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_microtopics_pagination_universal(
            callback=callback,
            state=state,
            callback_prefix=summary_callback_prefix,
            display_mode="summary",
            role=role
        )

    # Универсальный обработчик кнопки "Назад" из изображений микротем
    @router.callback_query(F.data == "back_from_microtopics_image")
    async def back_from_microtopics_image_handler(callback: CallbackQuery, state: FSMContext):
        logging.info(f"ВЫЗОВ: back_from_microtopics_image | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
        await handle_back_from_microtopics_image(
            callback=callback,
            state=state,
            back_keyboard_func=back_keyboard_func
        )
